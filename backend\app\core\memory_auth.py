"""
内存认证系统 - 临时解决方案
当数据库不可用时使用内存存储用户信息
"""

from typing import Dict, Optional
from datetime import datetime
from app.core.security import get_password_hash, verify_password
from app.schemas.user import User as UserSchema

class MemoryUserStore:
    """内存用户存储"""
    
    def __init__(self):
        self.users: Dict[str, dict] = {}
        self.user_counter = 1

        # 🎯 修复：创建默认测试用户（使用UUID格式的ID）
        self._create_default_users()

    def reset_users(self):
        """重置用户数据（用于修复ID格式问题）"""
        self.users.clear()
        self.user_counter = 1
        self._create_default_users()
    
    def _create_default_users(self):
        """创建默认测试用户"""
        default_users = [
            {
                "username": "admin",
                "email": "<EMAIL>",
                "password": "admin123",
                "full_name": "系统管理员",
                "is_admin": True
            },
            {
                "username": "testuser",
                "email": "<EMAIL>", 
                "password": "test123",
                "full_name": "测试用户",
                "is_admin": False
            },
            {
                "username": "demo",
                "email": "<EMAIL>",
                "password": "demo123", 
                "full_name": "演示用户",
                "is_admin": False
            }
        ]
        
        for user_data in default_users:
            self.create_user(
                username=user_data["username"],
                email=user_data["email"],
                password=user_data["password"],
                full_name=user_data["full_name"],
                is_admin=user_data.get("is_admin", False)
            )
    
    def create_user(self, username: str, email: str, password: str, 
                   full_name: str = None, phone: str = None, is_admin: bool = False) -> dict:
        """创建用户"""
        
        # 检查用户名和邮箱是否已存在
        for user in self.users.values():
            if user["username"] == username:
                raise ValueError("用户名已存在")
            if user["email"] == email:
                raise ValueError("邮箱已被注册")
        
        # 🎯 修复：生成UUID格式的用户ID而不是简单的计数器
        import uuid
        user_id = str(uuid.uuid4())
        self.user_counter += 1
        
        user_data = {
            "user_id": user_id,
            "username": username,
            "email": email,
            "password_hash": get_password_hash(password),
            "full_name": full_name or username,
            "phone": phone,
            "is_active": True,
            "is_verified": True,
            "is_admin": is_admin,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "last_login": None
        }
        
        self.users[user_id] = user_data
        return user_data
    
    def get_user_by_username(self, username: str) -> Optional[dict]:
        """根据用户名获取用户"""
        for user in self.users.values():
            if user["username"] == username:
                return user
        return None
    
    def get_user_by_email(self, email: str) -> Optional[dict]:
        """根据邮箱获取用户"""
        for user in self.users.values():
            if user["email"] == email:
                return user
        return None
    
    def get_user_by_id(self, user_id: str) -> Optional[dict]:
        """根据ID获取用户"""
        return self.users.get(user_id)
    
    def authenticate_user(self, username: str, password: str) -> Optional[dict]:
        """认证用户"""
        # 尝试用户名登录
        user = self.get_user_by_username(username)
        if not user:
            # 尝试邮箱登录
            user = self.get_user_by_email(username)
        
        if not user:
            return None
        
        if not verify_password(password, user["password_hash"]):
            return None
        
        if not user["is_active"]:
            return None
        
        # 更新最后登录时间
        user["last_login"] = datetime.utcnow().isoformat()
        
        return user
    
    def update_user(self, user_id: str, **kwargs) -> Optional[dict]:
        """更新用户信息"""
        user = self.users.get(user_id)
        if not user:
            return None
        
        # 更新允许的字段
        allowed_fields = ["full_name", "phone", "email", "is_active", "is_verified"]
        for field in allowed_fields:
            if field in kwargs:
                user[field] = kwargs[field]
        
        user["updated_at"] = datetime.utcnow().isoformat()
        return user
    
    def list_users(self) -> list:
        """获取所有用户列表"""
        return list(self.users.values())
    
    def delete_user(self, user_id: str) -> bool:
        """删除用户"""
        if user_id in self.users:
            del self.users[user_id]
            return True
        return False

# 全局内存用户存储实例
memory_user_store = None

def get_memory_user_store() -> MemoryUserStore:
    """获取内存用户存储实例"""
    global memory_user_store
    if memory_user_store is None:
        # 🎯 修复：创建新的实例以确保使用UUID格式的用户ID
        memory_user_store = MemoryUserStore()
    return memory_user_store

"""
应用配置管理
"""
import os
from pathlib import Path
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import field_validator

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

class Settings(BaseSettings):
    """应用设置"""
    
    # 基本应用信息
    APP_NAME: str = "智能法律文书生成系统"
    APP_VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"

    # 🎯 安全配置：控制是否允许内存认证回退
    ALLOW_MEMORY_AUTH_FALLBACK: bool = True  # 开发环境默认允许，生产环境应设为False
    
    # 数据库配置
    DATABASE_URL: str = "postgresql://legal_user:LegalDoc2024%21%40%23@***************:9856/legal_documents"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # JWT配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # AI模型配置
    OPENAI_API_KEY: Optional[str] = None
    CLAUDE_API_KEY: Optional[str] = None
    DEFAULT_AI_MODEL: str = "claude_3_sonnet"
    AI_TEMPERATURE: float = 0.1
    AI_MAX_TOKENS: int = 2000
    AI_TIMEOUT: int = 30
    
    # 语音识别配置
    SPEECH_API_KEY: Optional[str] = None
    SPEECH_REGION: Optional[str] = None
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 10485760  # 10MB
    UPLOAD_DIR: str = "./uploads"

    # 文档生成配置
    DOCUMENTS_OUTPUT_DIR: str = "documents/output"
    TEMPLATES_DIR: str = "templates"
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # 安全配置
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v

    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def assemble_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        env_file = ".env.dev"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的环境变量

# 创建全局设置实例
settings = Settings()

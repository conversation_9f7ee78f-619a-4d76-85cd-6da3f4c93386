"""
安全相关功能：JWT认证、密码加密等
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.database import get_db
from app.models.user import User

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT Bearer认证
security = HTTPBearer()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """验证令牌"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        return None

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        payload = verify_token(token)
        if payload is None:
            raise credentials_exception
        
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    # 🎯 修复：支持数据库和内存认证
    try:
        # 首先尝试从数据库获取用户
        user = db.query(User).filter(User.user_id == user_id).first()
        if user is not None:
            if not user.is_active:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户账户已被禁用"
                )
            return user
    except Exception:
        # 数据库查询失败，尝试内存认证
        pass

    # 🎯 安全修复：只在配置允许时才尝试内存认证
    from app.core.config import settings
    if settings.ALLOW_MEMORY_AUTH_FALLBACK:
        # 如果数据库中没有找到用户，尝试从内存存储获取
        try:
            from app.core.memory_auth import get_memory_user_store
            memory_store = get_memory_user_store()
            user_dict = memory_store.get_user_by_id(user_id)

            if user_dict is None:
                raise credentials_exception

            if not user_dict.get("is_active", True):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户账户已被禁用"
                )

            # 将字典转换为User对象（用于兼容性）
            from datetime import datetime
            user_obj = User()
            user_obj.user_id = user_dict["user_id"]
            user_obj.username = user_dict["username"]
            user_obj.email = user_dict["email"]
            user_obj.full_name = user_dict.get("full_name")
            user_obj.phone = user_dict.get("phone")
            user_obj.is_active = user_dict.get("is_active", True)
            user_obj.is_verified = user_dict.get("is_verified", True)
            user_obj.is_admin = user_dict.get("is_admin", False)
            user_obj.created_at = datetime.fromisoformat(user_dict["created_at"]) if user_dict.get("created_at") else datetime.utcnow()
            user_obj.updated_at = datetime.fromisoformat(user_dict["updated_at"]) if user_dict.get("updated_at") else None
            user_obj.last_login = datetime.fromisoformat(user_dict["last_login"]) if user_dict.get("last_login") else None

            return user_obj

        except Exception:
            # 内存认证也失败
            pass

    # 数据库和内存认证都失败，或者不允许内存认证回退
    raise credentials_exception

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    return current_user

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """认证用户"""
    user = db.query(User).filter(
        (User.username == username) | (User.email == username)
    ).first()
    
    if not user:
        return None
    
    if not verify_password(password, user.password_hash):
        return None
    
    return user

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/login/page";
exports.ids = ["app/auth/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(rsc)/./src/app/auth/login/page.tsx\")), \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/login/page\",\n        pathname: \"/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkglM0ElNUMlNUNJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIySCUzQSU1QyU1Q0ludGVsbGlnZW50X2xlZ2FsX2RvY3VtZW50X2dlbmVyYXRpb24lNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q3JlYWN0LWhvdC10b2FzdCU1QyU1Q2Rpc3QlNUMlNUNpbmRleC5tanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUb2FzdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkglM0ElNUMlNUNJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJIJTNBJTVDJTVDSW50ZWxsaWdlbnRfbGVnYWxfZG9jdW1lbnRfZ2VuZXJhdGlvbiU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3Byb3ZpZGVycy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcm92aWRlcnMlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNNQUFxSztBQUNySztBQUNBLHdLQUF1SiIsInNvdXJjZXMiOlsid2VicGFjazovL2xlZ2FsLWRvY3VtZW50LWFpLWZyb250ZW5kLz85MWVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclwiXSAqLyBcIkg6XFxcXEludGVsbGlnZW50X2xlZ2FsX2RvY3VtZW50X2dlbmVyYXRpb25cXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUHJvdmlkZXJzXCJdICovIFwiSDpcXFxcSW50ZWxsaWdlbnRfbGVnYWxfZG9jdW1lbnRfZ2VuZXJhdGlvblxcXFxmcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/login/page.tsx */ \"(ssr)/./src/app/auth/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkglM0ElNUMlNUNJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhdXRoJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQXlIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGVnYWwtZG9jdW1lbnQtYWktZnJvbnRlbmQvP2M1YjUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJIOlxcXFxJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcYXV0aFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cauth%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Scale!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Scale!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Scale!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Loader2,Scale!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nconst loginSchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    username: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(1, \"请输入用户名或邮箱\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(1, \"请输入密码\")\n});\nfunction LoginPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { login, setLoading } = (0,_lib_auth__WEBPACK_IMPORTED_MODULE_8__.useAuthStore)();\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 获取重定向URL\n    const redirectUrl = searchParams.get(\"redirect\");\n    const { register, handleSubmit, formState: { errors, isSubmitting } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(loginSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_7__.api.auth.login(data);\n            login(response.data.access_token, response.data.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].success(\"登录成功！\");\n            // 根据用户类型和重定向URL决定跳转\n            const user = response.data.user;\n            let targetUrl = \"/dashboard\" // 默认跳转到仪表板\n            ;\n            if (redirectUrl) {\n                // 如果有重定向URL，使用重定向URL\n                targetUrl = redirectUrl;\n            } else if (user.is_admin) {\n                // 如果是管理员且没有重定向URL，跳转到管理后台\n                targetUrl = \"/admin\";\n            }\n            console.log(\"登录成功，跳转到:\", targetUrl);\n            router.push(targetUrl);\n        } catch (error) {\n            console.error(\"登录失败:\", error);\n            const message = error.response?.data?.message || \"登录失败，请检查用户名和密码\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_6__[\"default\"].error(message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            href: \"/\",\n                            className: \"inline-flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-10 w-10 text-primary-600\"\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"智能法律文书\"\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-bold text-gray-900\",\n                            children: \"登录您的账户\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: [\n                                \"还没有账户？\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    href: \"/auth/register\",\n                                    className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                    children: \"立即注册\"\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-content\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit(onSubmit),\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"用户名或邮箱\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"username\"),\n                                                    type: \"text\",\n                                                    autoComplete: \"username\",\n                                                    className: `input ${errors.username ? \"input-error\" : \"\"}`,\n                                                    placeholder: \"请输入用户名或邮箱\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.username.message\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"密码\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    ...register(\"password\"),\n                                                    type: showPassword ? \"text\" : \"password\",\n                                                    autoComplete: \"current-password\",\n                                                    className: `input pr-10 ${errors.password ? \"input-error\" : \"\"}`,\n                                                    placeholder: \"请输入密码\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-red-600\",\n                                                    children: errors.password.message\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"remember-me\",\n                                                    name: \"remember-me\",\n                                                    type: \"checkbox\",\n                                                    className: \"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"remember-me\",\n                                                    className: \"ml-2 block text-sm text-gray-900\",\n                                                    children: \"记住我\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                href: \"/auth/forgot-password\",\n                                                className: \"font-medium text-primary-600 hover:text-primary-500\",\n                                                children: \"忘记密码？\"\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"btn btn-primary w-full btn-lg\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Loader2_Scale_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"登录中...\"\n                                            ]\n                                        }, void 0, true) : \"登录\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full border-t border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex justify-center text-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 bg-gray-50 text-gray-500\",\n                                        children: \"或者\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/demo\",\n                                className: \"btn btn-outline w-full btn-lg\",\n                                children: \"体验演示版本\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\n                            \"登录即表示您同意我们的\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/terms\",\n                                className: \"text-primary-600 hover:text-primary-500\",\n                                children: \"服务条款\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this),\n                            \" \",\n                            \"和\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                href: \"/privacy\",\n                                className: \"text-primary-600 hover:text-primary-500\",\n                                children: \"隐私政策\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                const FallbackComponent = this.props.fallback;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                    error: this.state.error,\n                    resetError: this.resetError\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 40,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 text-center mb-2\",\n                    children: \"出现了一些问题\"\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center mb-6\",\n                    children: \"应用程序遇到了意外错误。请尝试刷新页面或联系技术支持。\"\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-sm text-gray-500 cursor-pointer hover:text-gray-700\",\n                            children: \"查看错误详情\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-3 rounded border overflow-auto max-h-32\",\n                            children: [\n                                error.message,\n                                error.stack && \"\\n\\n\" + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetError,\n                            className: \"flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"flex-1 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors\",\n                            children: \"刷新页面\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/lib/index.mjs\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(ssr)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _lib_queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/queryClient */ \"(ssr)/./src/lib/queryClient.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n            client: _lib_queryClient__WEBPACK_IMPORTED_MODULE_2__.queryClient,\n            children: [\n                children,\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                    initialIsOpen: false\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTJEO0FBQ1E7QUFDYjtBQUNQO0FBRXhDLFNBQVNJLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxxQkFDRSw4REFBQ0gsaUVBQWFBO2tCQUNaLDRFQUFDRixzRUFBbUJBO1lBQUNNLFFBQVFILHlEQUFXQTs7Z0JBQ3JDRTtnQkFYVCxLQVlrQyxrQkFDeEIsOERBQUNKLDhFQUFrQkE7b0JBQUNNLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGVnYWwtZG9jdW1lbnQtYWktZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4P2JlODciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFF1ZXJ5Q2xpZW50UHJvdmlkZXIgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknXG5pbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnXG5pbXBvcnQgRXJyb3JCb3VuZGFyeSBmcm9tICdAL2NvbXBvbmVudHMvRXJyb3JCb3VuZGFyeSdcbmltcG9ydCB7IHF1ZXJ5Q2xpZW50IH0gZnJvbSAnQC9saWIvcXVlcnlDbGllbnQnXG5cbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxFcnJvckJvdW5kYXJ5PlxuICAgICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAge3Byb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnICYmIChcbiAgICAgICAgICA8UmVhY3RRdWVyeURldnRvb2xzIGluaXRpYWxJc09wZW49e2ZhbHNlfSAvPlxuICAgICAgICApfVxuICAgICAgPC9RdWVyeUNsaWVudFByb3ZpZGVyPlxuICAgIDwvRXJyb3JCb3VuZGFyeT5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJSZWFjdFF1ZXJ5RGV2dG9vbHMiLCJFcnJvckJvdW5kYXJ5IiwicXVlcnlDbGllbnQiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImNsaWVudCIsImluaXRpYWxJc09wZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/**\n * API客户端配置\n */ \n\n\n// API基础配置 - 直接连接后端API，避免路径重复\nconst API_BASE_URL = \"http://127.0.0.1:8000\" || 0;\n// 创建axios实例\nconst apiClient = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n    baseURL: `${API_BASE_URL}/api/v1`,\n    timeout: 30000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// 请求拦截器\napiClient.interceptors.request.use((config)=>{\n    const { token } = _auth__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState();\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// 响应拦截器\napiClient.interceptors.response.use((response)=>{\n    return response;\n}, (error)=>{\n    const { response } = error;\n    if (response?.status === 401) {\n        // Token过期或无效，清除认证状态\n        _auth__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState().logout();\n        // 如果不在登录页面，跳转到登录页面\n        if (false) {}\n    } else if (response?.status >= 500) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(\"服务器错误，请稍后重试\");\n    } else if (response?.data?.message) {\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_1__[\"default\"].error(response.data.message);\n    }\n    return Promise.reject(error);\n});\n// API方法\nconst api = {\n    // 认证相关\n    auth: {\n        login: (data)=>apiClient.post(\"/auth/login\", data),\n        register: (data)=>apiClient.post(\"/auth/register\", data),\n        refreshToken: ()=>apiClient.post(\"/auth/refresh\"),\n        getProfile: ()=>apiClient.get(\"/auth/me\")\n    },\n    // 用户相关\n    users: {\n        updateProfile: (data)=>apiClient.put(\"/users/me\", data)\n    },\n    // AI服务相关\n    ai: {\n        getModels: ()=>apiClient.get(\"/ai/models\"),\n        analyzeCase: (data)=>apiClient.post(\"/ai/analyze-case\", data),\n        extractEntities: (data)=>apiClient.post(\"/ai/extract-entities\", data),\n        generateDocument: (data)=>apiClient.post(\"/ai/generate-document\", data),\n        processConversation: (data)=>apiClient.post(\"/ai/conversation\", data),\n        validateContent: (data)=>apiClient.post(\"/ai/validate-content\", data),\n        suggestImprovements: (params)=>apiClient.post(\"/ai/suggest-improvements\", null, {\n                params\n            })\n    },\n    // 案件管理相关\n    cases: {\n        list: (params)=>apiClient.get(\"/cases/\", {\n                params\n            }),\n        create: (data)=>apiClient.post(\"/cases/\", data),\n        get: (caseId)=>apiClient.get(`/cases/${caseId}`),\n        update: (caseId, data)=>apiClient.put(`/cases/${caseId}`, data),\n        delete: (caseId)=>apiClient.delete(`/cases/${caseId}`),\n        // 当事人管理\n        addParty: (caseId, data)=>apiClient.post(`/cases/${caseId}/parties`, data),\n        listParties: (caseId)=>apiClient.get(`/cases/${caseId}/parties`),\n        updateParty: (caseId, partyId, data)=>apiClient.put(`/cases/${caseId}/parties/${partyId}`, data),\n        deleteParty: (caseId, partyId)=>apiClient.delete(`/cases/${caseId}/parties/${partyId}`)\n    },\n    // 文档生成相关\n    documents: {\n        generate: (caseId, data)=>apiClient.post(`/documents/generate/${caseId}`, data),\n        download: (documentId)=>apiClient.get(`/documents/download/${documentId}`, {\n                responseType: \"blob\"\n            }),\n        listTemplates: (_params)=>Promise.resolve({\n                data: {\n                    templates: [],\n                    total: 0\n                }\n            }),\n        getHistory: (params)=>apiClient.get(\"/documents/history\", {\n                params\n            })\n    },\n    // 聊天相关\n    chat: {\n        // 创建会话\n        createSession: ()=>apiClient.post(\"/chat/sessions\"),\n        // 获取会话列表\n        getSessions: (params)=>apiClient.get(\"/chat/sessions\", {\n                params\n            }),\n        // 获取会话历史\n        getHistory: (sessionId, params)=>apiClient.get(`/chat/sessions/${sessionId}/history`, {\n                params\n            }),\n        // 获取会话上下文\n        getContext: (sessionId)=>apiClient.get(`/chat/sessions/${sessionId}/context`),\n        // 删除会话\n        deleteSession: (sessionId)=>apiClient.delete(`/chat/sessions/${sessionId}`),\n        // 发送消息（HTTP方式）\n        sendMessage: (sessionId, data)=>apiClient.post(`/chat/sessions/${sessionId}/messages`, data),\n        // 获取WebSocket统计\n        getWebSocketStats: ()=>apiClient.get(\"/chat/websocket/stats\")\n    },\n    // 推荐相关\n    recommendations: {\n        // 获取推荐内容\n        getRecommendations: (sessionId, params)=>apiClient.get(`/recommendations/${sessionId}`, {\n                params\n            }),\n        // 获取热门内容\n        getPopularContent: (params)=>apiClient.get(\"/recommendations/popular\", {\n                params\n            })\n    },\n    // 模板相关API已移除，请使用知识库API\n    // 案例相关\n    legalCases: {\n        // 获取案例列表\n        list: (params)=>apiClient.get(\"/cases\", {\n                params\n            }),\n        // 获取案例详情\n        get: (caseId)=>apiClient.get(`/cases/${caseId}`),\n        // 搜索案例\n        search: (params)=>apiClient.get(\"/cases/search\", {\n                params\n            }),\n        // 获取相似案例\n        similar: (caseId, params)=>apiClient.get(`/cases/${caseId}/similar`, {\n                params\n            }),\n        // 获取热门案例\n        popular: (params)=>apiClient.get(\"/cases/popular\", {\n                params\n            })\n    },\n    // 知识库相关\n    knowledgeBase: {\n        // 获取统计信息\n        getStats: ()=>apiClient.get(\"/admin/knowledge-base/stats/public\"),\n        // 获取分类\n        getCategories: ()=>apiClient.get(\"/admin/knowledge-base/categories/public\"),\n        // 获取条目列表\n        getEntries: (params)=>apiClient.get(\"/admin/knowledge-base/entries/public\", {\n                params\n            }),\n        // 获取单个条目\n        getEntry: (entryId)=>apiClient.get(`/admin/knowledge-base/entries/public/${entryId}`),\n        // 创建条目\n        createEntry: (data)=>apiClient.post(\"/admin/knowledge-base/entries/public\", data),\n        // 更新条目\n        updateEntry: (entryId, data)=>apiClient.put(`/admin/knowledge-base/entries/public/${entryId}`, data),\n        // 删除条目\n        deleteEntry: (entryId)=>apiClient.delete(`/admin/knowledge-base/entries/public/${entryId}`),\n        // 搜索条目\n        searchEntries: (data)=>apiClient.post(\"/admin/knowledge-base/search\", data)\n    },\n    // 指南相关\n    guides: {\n        // 获取指南列表\n        list: (params)=>apiClient.get(\"/guides\", {\n                params\n            }),\n        // 获取指南详情\n        get: (guideId)=>apiClient.get(`/guides/${guideId}`),\n        // 搜索指南\n        search: (params)=>apiClient.get(\"/guides/search\", {\n                params\n            }),\n        // 获取热门指南\n        popular: (params)=>apiClient.get(\"/guides/popular\", {\n                params\n            })\n    },\n    // 系统相关\n    system: {\n        // 健康检查\n        health: ()=>apiClient.get(\"/health\"),\n        // 获取WebSocket信息\n        websocketInfo: ()=>apiClient.get(\"/websocket/info\")\n    }\n};\n// 导出apiClient实例\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAuthHeaders: () => (/* binding */ getAuthHeaders),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * 认证相关工具函数\n */ \n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: true,\n        login: (token, user)=>{\n            console.log(\"[Auth] 用户登录:\", {\n                username: user.username,\n                is_admin: user.is_admin\n            });\n            set({\n                token,\n                user,\n                isAuthenticated: true,\n                isLoading: false\n            });\n        },\n        logout: ()=>{\n            console.log(\"[Auth] 用户登出\");\n            set({\n                token: null,\n                user: null,\n                isAuthenticated: false,\n                isLoading: false\n            });\n        },\n        updateUser: (user)=>{\n            set({\n                user\n            });\n        },\n        setLoading: (loading)=>{\n            set({\n                isLoading: loading\n            });\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            token: state.token,\n            user: state.user,\n            isAuthenticated: state.isAuthenticated\n        }),\n    onRehydrateStorage: ()=>(state)=>{\n            console.log(\"[Auth] 持久化状态恢复:\", state ? {\n                hasToken: !!state.token,\n                hasUser: !!state.user,\n                isAuthenticated: state.isAuthenticated\n            } : \"null\");\n            // 恢复完成后设置加载状态为false\n            if (state) {\n                state.isLoading = false;\n            }\n        }\n}));\n// 检查token是否过期\nfunction isTokenExpired(token) {\n    try {\n        const payload = JSON.parse(atob(token.split(\".\")[1]));\n        const currentTime = Date.now() / 1000;\n        return payload.exp < currentTime;\n    } catch  {\n        return true;\n    }\n}\n// 获取认证头\nfunction getAuthHeaders() {\n    const { token } = useAuthStore.getState();\n    if (!token || isTokenExpired(token)) {\n        useAuthStore.getState().logout();\n        return {};\n    }\n    return {\n        Authorization: `Bearer ${token}`\n    };\n}\n// 检查用户是否已认证\nfunction useAuth() {\n    const { user, token, isAuthenticated, isLoading } = useAuthStore();\n    // 如果正在加载，返回加载状态\n    if (isLoading) {\n        return {\n            user: null,\n            token: null,\n            isAuthenticated: false,\n            isLoading: true\n        };\n    }\n    // 如果没有token，直接返回未认证\n    if (!token) {\n        return {\n            user,\n            token,\n            isAuthenticated: false,\n            isLoading: false\n        };\n    }\n    // 检查token是否过期\n    const isValidAuth = isAuthenticated && token && !isTokenExpired(token);\n    // 如果token过期，自动登出\n    if (!isValidAuth && token) {\n        console.log(\"[Auth] Token过期，自动登出\");\n        useAuthStore.getState().logout();\n        return {\n            user: null,\n            token: null,\n            isAuthenticated: false,\n            isLoading: false\n        };\n    }\n    return {\n        user,\n        token,\n        isAuthenticated: isValidAuth,\n        isLoading: false\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/queryClient.ts":
/*!********************************!*\
  !*** ./src/lib/queryClient.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryClient: () => (/* binding */ queryClient),\n/* harmony export */   queryKeys: () => (/* binding */ queryKeys),\n/* harmony export */   queryOptions: () => (/* binding */ queryOptions)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryClient.mjs\");\n\nconst queryConfig = {\n    queries: {\n        // 数据保持新鲜的时间（5分钟）\n        staleTime: 5 * 60 * 1000,\n        // 缓存时间（10分钟）\n        gcTime: 10 * 60 * 1000,\n        // 重试配置\n        retry: (failureCount, error)=>{\n            // 对于4xx错误不重试\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            // 最多重试3次\n            return failureCount < 3;\n        },\n        // 重试延迟\n        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n        // 窗口重新获得焦点时重新获取数据\n        refetchOnWindowFocus: false,\n        // 网络重连时重新获取数据\n        refetchOnReconnect: true\n    },\n    mutations: {\n        // 变更重试配置\n        retry: (failureCount, error)=>{\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            return failureCount < 2;\n        }\n    }\n};\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.QueryClient({\n    defaultOptions: queryConfig\n});\n// 查询键工厂\nconst queryKeys = {\n    // 聊天相关\n    chat: {\n        all: [\n            \"chat\"\n        ],\n        sessions: ()=>[\n                ...queryKeys.chat.all,\n                \"sessions\"\n            ],\n        session: (id)=>[\n                ...queryKeys.chat.sessions(),\n                id\n            ],\n        history: (sessionId, page)=>[\n                ...queryKeys.chat.session(sessionId),\n                \"history\",\n                page\n            ],\n        context: (sessionId)=>[\n                ...queryKeys.chat.session(sessionId),\n                \"context\"\n            ]\n    },\n    // 推荐相关\n    recommendations: {\n        all: [\n            \"recommendations\"\n        ],\n        session: (sessionId)=>[\n                ...queryKeys.recommendations.all,\n                \"session\",\n                sessionId\n            ],\n        popular: (category)=>[\n                ...queryKeys.recommendations.all,\n                \"popular\",\n                category\n            ]\n    },\n    // 模板相关\n    templates: {\n        all: [\n            \"templates\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.templates.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.templates.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.templates.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.templates.all,\n                \"search\",\n                query,\n                filters\n            ]\n    },\n    // 案例相关\n    cases: {\n        all: [\n            \"cases\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.cases.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.cases.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.cases.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.cases.all,\n                \"search\",\n                query,\n                filters\n            ],\n        similar: (id)=>[\n                ...queryKeys.cases.all,\n                \"similar\",\n                id\n            ]\n    },\n    // 知识库相关\n    knowledge: {\n        all: [\n            \"knowledge\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.knowledge.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.knowledge.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.knowledge.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.knowledge.all,\n                \"search\",\n                query,\n                filters\n            ],\n        categories: ()=>[\n                ...queryKeys.knowledge.all,\n                \"categories\"\n            ]\n    },\n    // 指南相关\n    guides: {\n        all: [\n            \"guides\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.guides.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.guides.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.guides.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.guides.all,\n                \"search\",\n                query,\n                filters\n            ]\n    },\n    // 用户相关\n    user: {\n        all: [\n            \"user\"\n        ],\n        profile: ()=>[\n                ...queryKeys.user.all,\n                \"profile\"\n            ],\n        preferences: ()=>[\n                ...queryKeys.user.all,\n                \"preferences\"\n            ],\n        history: ()=>[\n                ...queryKeys.user.all,\n                \"history\"\n            ]\n    },\n    // 系统相关\n    system: {\n        all: [\n            \"system\"\n        ],\n        health: ()=>[\n                ...queryKeys.system.all,\n                \"health\"\n            ],\n        stats: ()=>[\n                ...queryKeys.system.all,\n                \"stats\"\n            ],\n        websocket: ()=>[\n                ...queryKeys.system.all,\n                \"websocket\"\n            ]\n    }\n};\n// 查询选项预设\nconst queryOptions = {\n    // 实时数据（短缓存）\n    realtime: {\n        staleTime: 0,\n        gcTime: 1 * 60 * 1000,\n        refetchInterval: 30 * 1000\n    },\n    // 静态数据（长缓存）\n    static: {\n        staleTime: 60 * 60 * 1000,\n        gcTime: 24 * 60 * 60 * 1000,\n        refetchOnWindowFocus: false,\n        refetchOnReconnect: false\n    },\n    // 用户数据（中等缓存）\n    user: {\n        staleTime: 10 * 60 * 1000,\n        gcTime: 30 * 60 * 1000\n    },\n    // 搜索结果（短缓存）\n    search: {\n        staleTime: 2 * 60 * 1000,\n        gcTime: 5 * 60 * 1000\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/queryClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dca4c53cce0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGVnYWwtZG9jdW1lbnQtYWktZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzA3OGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkY2E0YzUzY2NlMGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`H:\Intelligent_legal_document_generation\frontend\src\app\auth\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"智能法律文书生成系统\",\n    description: \"基于AI大模型的智能法律文书生成应用，支持移动端优先的响应式设计\",\n    keywords: [\n        \"法律文书\",\n        \"AI\",\n        \"智能生成\",\n        \"起诉状\",\n        \"答辞状\"\n    ],\n    authors: [\n        {\n            name: \"智能法律文书生成系统\"\n        }\n    ],\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1\n    },\n    themeColor: \"#3b82f6\",\n    manifest: \"/manifest.json\",\n    icons: {\n        icon: \"/favicon.ico\",\n        apple: \"/apple-touch-icon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-full\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-center\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#10b981\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`H:\Intelligent_legal_document_generation\frontend\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@tanstack","vendor-chunks/superjson","vendor-chunks/react-hot-toast","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/is-what","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/copy-anything","vendor-chunks/axios","vendor-chunks/zod","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/zustand","vendor-chunks/get-proto","vendor-chunks/@hookform","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/react-hook-form","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth%2Flogin%2Fpage&page=%2Fauth%2Flogin%2Fpage&appPaths=%2Fauth%2Flogin%2Fpage&pagePath=private-next-app-dir%2Fauth%2Flogin%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
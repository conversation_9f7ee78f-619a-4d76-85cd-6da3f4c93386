#!/usr/bin/env python3
"""
测试UUID修复是否生效
"""
import requests
import json
import uuid

def test_uuid_fix():
    """测试UUID修复"""
    base_url = "http://127.0.0.1:8000/api/v1"
    
    print("🔍 测试UUID修复...")
    
    # 测试登录
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        print("\n1. 测试管理员登录...")
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 登录成功!")
            
            # 检查用户ID格式
            user_id = data["user"]["user_id"]
            print(f"用户ID: {user_id}")
            print(f"用户ID类型: {type(user_id)}")
            
            # 验证是否为有效的UUID格式
            try:
                uuid_obj = uuid.UUID(user_id)
                print(f"✅ 用户ID是有效的UUID: {uuid_obj}")
                print(f"UUID版本: {uuid_obj.version}")
            except ValueError as e:
                print(f"❌ 用户ID不是有效的UUID: {e}")
                return False
            
            # 测试获取用户信息
            print("\n2. 测试获取用户信息...")
            headers = {"Authorization": f"Bearer {data['access_token']}"}
            me_response = requests.get(f"{base_url}/auth/me", headers=headers)
            print(f"状态码: {me_response.status_code}")
            
            if me_response.status_code == 200:
                me_data = me_response.json()
                print("✅ 获取用户信息成功!")
                print(f"用户信息: {json.dumps(me_data, indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 获取用户信息失败: {me_response.text}")
                return False
                
        else:
            print(f"❌ 登录失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

def test_memory_auth_uuid():
    """测试内存认证系统的UUID生成"""
    print("\n🔍 测试内存认证系统UUID生成...")
    
    try:
        from app.core.memory_auth import get_memory_user_store
        
        # 获取内存用户存储
        store = get_memory_user_store()
        
        # 检查现有用户的ID格式
        users = store.list_users()
        print(f"用户数量: {len(users)}")
        
        for user in users:
            user_id = user["user_id"]
            username = user["username"]
            print(f"\n用户: {username}")
            print(f"用户ID: {user_id}")
            print(f"用户ID类型: {type(user_id)}")
            
            # 验证UUID格式
            try:
                uuid_obj = uuid.UUID(user_id)
                print(f"✅ 用户ID是有效的UUID: {uuid_obj}")
            except ValueError as e:
                print(f"❌ 用户ID不是有效的UUID: {e}")
                return False
        
        print("\n✅ 所有用户ID都是有效的UUID格式!")
        return True
        
    except Exception as e:
        print(f"❌ 测试内存认证系统时出现错误: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("UUID修复测试")
    print("=" * 50)
    
    # 测试内存认证系统
    memory_test_passed = test_memory_auth_uuid()
    
    # 测试API接口
    api_test_passed = test_uuid_fix()
    
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    print(f"内存认证系统测试: {'✅ 通过' if memory_test_passed else '❌ 失败'}")
    print(f"API接口测试: {'✅ 通过' if api_test_passed else '❌ 失败'}")
    
    if memory_test_passed and api_test_passed:
        print("\n🎉 UUID修复成功！所有测试通过！")
    else:
        print("\n⚠️ 部分测试失败，请检查修复情况")

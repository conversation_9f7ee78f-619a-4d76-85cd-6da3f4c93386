"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.json/route";
exports.ids = ["app/manifest.json/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_page_2Fmanifest_json_2Froute_filePath_H_3A_5CIntelligent_legal_document_generation_5Cfrontend_5Csrc_5Capp_5Cmanifest_json_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?page=%2Fmanifest.json%2Froute&filePath=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__ */ \"(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/manifest.json/route\",\n        pathname: \"/manifest.json\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.json/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?page=%2Fmanifest.json%2Froute&filePath=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_page_2Fmanifest_json_2Froute_filePath_H_3A_5CIntelligent_legal_document_generation_5Cfrontend_5Csrc_5Capp_5Cmanifest_json_isDynamic_0_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/manifest.json/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__ ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(app-metadata-route)/./node_modules/next/dist/api/server.js\");\n/* static asset route */\n\n\nconst contentType = \"application/manifest+json\"\nconst buffer = Buffer.from(\"ewogICJuYW1lIjogIuaZuuiDveazleW+i+aWh+S5pueUn+aIkOezu+e7nyIsCiAgInNob3J0X25hbWUiOiAi5rOV5b6L5paH5LmmQUkiLAogICJkZXNjcmlwdGlvbiI6ICLln7rkuo5BSeWkp+aooeWei+eahOaZuuiDveazleW+i+aWh+S5pueUn+aIkOW6lOeUqCIsCiAgInN0YXJ0X3VybCI6ICIvIiwKICAiZGlzcGxheSI6ICJzdGFuZGFsb25lIiwKICAiYmFja2dyb3VuZF9jb2xvciI6ICIjZmZmZmZmIiwKICAidGhlbWVfY29sb3IiOiAiIzNiODJmNiIsCiAgIm9yaWVudGF0aW9uIjogInBvcnRyYWl0LXByaW1hcnkiLAogICJpY29ucyI6IFsKICAgIHsKICAgICAgInNyYyI6ICIvaWNvbi0xOTJ4MTkyLnBuZyIsCiAgICAgICJzaXplcyI6ICIxOTJ4MTkyIiwKICAgICAgInR5cGUiOiAiaW1hZ2UvcG5nIiwKICAgICAgInB1cnBvc2UiOiAibWFza2FibGUiCiAgICB9LAogICAgewogICAgICAic3JjIjogIi9pY29uLTUxMng1MTIucG5nIiwKICAgICAgInNpemVzIjogIjUxMng1MTIiLAogICAgICAidHlwZSI6ICJpbWFnZS9wbmciLAogICAgICAicHVycG9zZSI6ICJtYXNrYWJsZSIKICAgIH0KICBdLAogICJjYXRlZ29yaWVzIjogWyJwcm9kdWN0aXZpdHkiLCAiYnVzaW5lc3MiLCAidXRpbGl0aWVzIl0sCiAgImxhbmciOiAiemgtQ04iLAogICJkaXIiOiAibHRyIgp9Cg==\", 'base64'\n  )\n\nfunction GET() {\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"no-cache, no-store\",\n    },\n  })\n}\n\nconst dynamic = 'force-static'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-metadata-route)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?page=%2Fmanifest.json%2Froute&filePath=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp%5Cmanifest.json&isDynamic=0!?__next_metadata_route__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fmanifest.json%2Froute&page=%2Fmanifest.json%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.json&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
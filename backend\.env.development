# 开发环境配置
APP_NAME=智能法律文书生成系统
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=postgresql://legal_user:LegalDoc2024%21%40%23@192.168.100.110:9856/legal_documents

# Redis配置
REDIS_URL=redis://192.168.100.110:9857

# JWT配置
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=120
ALGORITHM=HS256

# 🎯 安全配置
# 开发环境允许内存认证回退，方便开发调试
ALLOW_MEMORY_AUTH_FALLBACK=true

# AI配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
DEFAULT_MODEL=gpt-3.5-turbo

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# 日志配置
LOG_LEVEL=DEBUG
LOG_FILE=logs/app.log

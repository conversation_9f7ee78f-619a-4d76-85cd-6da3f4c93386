#!/usr/bin/env python3
"""
测试安全配置是否正确工作
"""
import os
import requests
import json

def test_with_memory_fallback_enabled():
    """测试启用内存认证回退时的行为"""
    print("🔍 测试启用内存认证回退...")
    
    # 设置环境变量
    os.environ["ALLOW_MEMORY_AUTH_FALLBACK"] = "true"
    
    base_url = "http://127.0.0.1:8000/api/v1"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 内存认证回退启用时，登录成功（预期行为）")
            return True
        else:
            print(f"❌ 内存认证回退启用时，登录失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False

def test_with_memory_fallback_disabled():
    """测试禁用内存认证回退时的行为"""
    print("\n🔍 测试禁用内存认证回退...")
    
    # 设置环境变量
    os.environ["ALLOW_MEMORY_AUTH_FALLBACK"] = "false"
    
    base_url = "http://127.0.0.1:8000/api/v1"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 503:
            print("✅ 内存认证回退禁用时，返回503服务不可用（预期行为）")
            return True
        elif response.status_code == 200:
            print("⚠️ 内存认证回退禁用时，仍然登录成功（可能数据库可用）")
            return True
        else:
            print(f"❌ 内存认证回退禁用时，返回意外状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False

def test_database_connection():
    """测试数据库连接状态"""
    print("\n🔍 测试数据库连接状态...")
    
    try:
        import psycopg2
        conn_str = "postgresql://legal_user:LegalDoc2024%21%40%23@192.168.100.110:9856/legal_documents"
        conn = psycopg2.connect(conn_str)
        conn.close()
        print("✅ 数据库连接正常")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def show_security_recommendations():
    """显示安全建议"""
    print("\n" + "="*60)
    print("🔒 安全配置建议")
    print("="*60)
    
    print("\n📋 环境配置:")
    print("• 开发环境: ALLOW_MEMORY_AUTH_FALLBACK=true")
    print("• 生产环境: ALLOW_MEMORY_AUTH_FALLBACK=false")
    
    print("\n🎯 当前数据库状态:")
    db_status = test_database_connection()
    if db_status:
        print("• ✅ 数据库可用 - 建议禁用内存认证回退")
        print("• 设置: ALLOW_MEMORY_AUTH_FALLBACK=false")
    else:
        print("• ❌ 数据库不可用 - 需要修复数据库连接")
        print("• 临时措施: ALLOW_MEMORY_AUTH_FALLBACK=true（仅开发环境）")
    
    print("\n⚠️ 安全警告:")
    print("• 内存认证使用默认凭据，不适用于生产环境")
    print("• 生产环境必须确保数据库可用并禁用内存认证回退")
    print("• 定期检查数据库连接状态")
    
    print("\n🛠️ 修复数据库连接的步骤:")
    print("1. 检查PostgreSQL服务是否在192.168.100.110:9856运行")
    print("2. 检查网络连接和防火墙设置")
    print("3. 验证数据库凭据和权限")
    print("4. 检查数据库配置文件")

if __name__ == "__main__":
    print("="*60)
    print("安全配置测试")
    print("="*60)
    
    # 显示安全建议
    show_security_recommendations()
    
    print("\n" + "="*60)
    print("功能测试")
    print("="*60)
    
    # 测试不同配置下的行为
    test1_passed = test_with_memory_fallback_enabled()
    test2_passed = test_with_memory_fallback_disabled()
    
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    print(f"内存认证回退启用测试: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"内存认证回退禁用测试: {'✅ 通过' if test2_passed else '❌ 失败'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 安全配置工作正常！")
    else:
        print("\n⚠️ 部分测试失败，请检查配置")
    
    print("\n💡 建议:")
    print("• 修复PostgreSQL数据库连接问题")
    print("• 生产环境设置 ALLOW_MEMORY_AUTH_FALLBACK=false")
    print("• 开发环境可以保持 ALLOW_MEMORY_AUTH_FALLBACK=true")

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: \"/manifest.json\"\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUglM0ElNUNJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uJTVDZnJvbnRlbmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUglM0ElNUNJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uJTVDZnJvbnRlbmQmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxhQUFhLHNCQUFzQjtBQUNpRTtBQUNyQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakMsdUJBQXVCLGdKQUE0RztBQUNuSTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIsb0pBQThHO0FBQ3ZJLG9CQUFvQiwwTkFBZ0Y7QUFDcEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ3VCO0FBQzZEO0FBQ3BGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ3VEO0FBQ3ZEO0FBQ08sd0JBQXdCLDhHQUFrQjtBQUNqRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sZWdhbC1kb2N1bWVudC1haS1mcm9udGVuZC8/MmY3MiJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJIOlxcXFxJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIiksIFwiSDpcXFxcSW50ZWxsaWdlbnRfbGVnYWxfZG9jdW1lbnRfZ2VuZXJhdGlvblxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdLFxuICAgICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogW10sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IFwiL21hbmlmZXN0Lmpzb25cIlxuICB9XG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJIOlxcXFxJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiKSwgXCJIOlxcXFxJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogW10sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IFwiL21hbmlmZXN0Lmpzb25cIlxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJIOlxcXFxJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers.tsx */ \"(ssr)/./src/components/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Cnode_modules%5C%5Creact-hot-toast%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkglM0ElNUMlNUNJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTRHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGVnYWwtZG9jdW1lbnQtYWktZnJvbnRlbmQvPzNjM2IiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJIOlxcXFxJbnRlbGxpZ2VudF9sZWdhbF9kb2N1bWVudF9nZW5lcmF0aW9uXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CIntelligent_legal_document_generation%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,FileText,Mic,Scale,Shield,Smartphone,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction HomePage() {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsVisible(true);\n    }, []);\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: \"智能法律分析\",\n            description: \"基于AI大模型的深度法律理解，准确识别案件类型和关键要素\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"语音交互\",\n            description: \"支持语音输入和智能问答，让法律文书填写像聊天一样简单\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"移动端优先\",\n            description: \"完美适配手机、平板等移动设备，随时随地处理法律事务\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"DOCX导出\",\n            description: \"生成符合官方要求的标准Word文档，可直接打印提交\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"数据安全\",\n            description: \"端到端加密保护，确保您的法律信息安全可靠\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: \"高效准确\",\n            description: \"基于官方范本，确保生成文书的法律准确性和规范性\"\n        }\n    ];\n    const caseTypes = [\n        \"离婚纠纷\",\n        \"买卖合同纠纷\",\n        \"民间借贷纠纷\",\n        \"交通事故纠纷\",\n        \"劳动争议\",\n        \"房屋租赁纠纷\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"safe-top relative z-10 bg-white/80 backdrop-blur-md border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mobile-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-8 w-8 text-primary-600\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-gray-900\",\n                                        children: \"智能法律文书\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/login\",\n                                        className: \"btn btn-ghost btn-sm\",\n                                        children: \"登录\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/auth/register\",\n                                        className: \"btn btn-primary btn-sm\",\n                                        children: \"注册\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative overflow-hidden py-20 sm:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: isVisible ? 1 : 0,\n                                    y: isVisible ? 0 : 20\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\",\n                                        children: [\n                                            \"AI驱动的\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-primary-600\",\n                                                children: \"智能法律文书\"\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"生成系统\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mx-auto mt-6 max-w-2xl text-lg leading-8 text-gray-600\",\n                                        children: \"通过语音或文字描述您的法律问题，AI将帮助您生成符合官方要求的起诉状、答辞状等法律文书。 移动端优先设计，让法律服务触手可及。\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/chat\",\n                                                className: \"btn btn-primary btn-lg\",\n                                                children: [\n                                                    \"开始智能对话\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/auth/register\",\n                                                className: \"btn btn-outline btn-lg\",\n                                                children: \"注册账户\"\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"features\",\n                        className: \"py-24 sm:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"为什么选择我们？\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"基于最新AI技术，为您提供专业、高效、安全的法律文书生成服务\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\",\n                                    children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"card hover:shadow-lg transition-shadow duration-300\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-12 w-12 items-center justify-center rounded-lg bg-primary-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                    className: \"h-6 w-6 text-primary-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 162,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900\",\n                                                                children: feature.title\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-4 text-gray-600\",\n                                                        children: feature.description\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, feature.title, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-gray-50 py-24 sm:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\",\n                                            children: \"支持的案件类型\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-4 text-lg text-gray-600\",\n                                            children: \"覆盖常见的民事、刑事、行政等各类法律纠纷\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-16 grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-6\",\n                                    children: caseTypes.map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.9\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            className: \"flex items-center justify-center rounded-lg bg-white p-4 shadow-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mx-auto h-6 w-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-2 text-sm font-medium text-gray-900\",\n                                                        children: type\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, type, false, {\n                                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-24 sm:py-32\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-container\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"rounded-2xl bg-primary-600 px-6 py-16 text-center sm:px-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-3xl font-bold tracking-tight text-white sm:text-4xl\",\n                                        children: \"准备好开始了吗？\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mx-auto mt-6 max-w-xl text-lg leading-8 text-primary-100\",\n                                        children: \"立即注册，体验AI驱动的智能法律文书生成服务，让法律服务变得简单高效。\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-10 flex items-center justify-center gap-x-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/chat\",\n                                                className: \"btn bg-white text-primary-600 hover:bg-gray-50 btn-lg\",\n                                                children: \"立即体验\"\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/auth/register\",\n                                                className: \"btn border-white text-white hover:bg-white/10 btn-lg\",\n                                                children: \"免费注册\"\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"safe-bottom bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mobile-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_FileText_Mic_Scale_Shield_Smartphone_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"h-6 w-6 text-primary-400\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"智能法律文书生成系统\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-center text-sm text-gray-400\",\n                                children: \"\\xa9 2024 智能法律文书生成系统. 保留所有权利.\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ErrorBoundary.tsx":
/*!******************************************!*\
  !*** ./src/components/ErrorBoundary.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends (react__WEBPACK_IMPORTED_MODULE_1___default().Component) {\n    constructor(props){\n        super(props);\n        this.resetError = ()=>{\n            this.setState({\n                hasError: false,\n                error: undefined\n            });\n        };\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true,\n            error\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n    }\n    render() {\n        if (this.state.hasError) {\n            if (this.props.fallback) {\n                const FallbackComponent = this.props.fallback;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FallbackComponent, {\n                    error: this.state.error,\n                    resetError: this.resetError\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DefaultErrorFallback, {\n                error: this.state.error,\n                resetError: this.resetError\n            }, void 0, false, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                lineNumber: 40,\n                columnNumber: 14\n            }, this);\n        }\n        return this.props.children;\n    }\n}\nfunction DefaultErrorFallback({ error, resetError }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6 text-red-600\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-semibold text-gray-900 text-center mb-2\",\n                    children: \"出现了一些问题\"\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center mb-6\",\n                    children: \"应用程序遇到了意外错误。请尝试刷新页面或联系技术支持。\"\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"text-sm text-gray-500 cursor-pointer hover:text-gray-700\",\n                            children: \"查看错误详情\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"mt-2 text-xs text-red-600 bg-red-50 p-3 rounded border overflow-auto max-h-32\",\n                            children: [\n                                error.message,\n                                error.stack && \"\\n\\n\" + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetError,\n                            className: \"flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n                            children: \"重试\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.reload(),\n                            className: \"flex-1 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors\",\n                            children: \"刷新页面\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/./node_modules/@tanstack/react-query-devtools/build/lib/index.mjs\");\n/* harmony import */ var _components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ErrorBoundary */ \"(ssr)/./src/components/ErrorBoundary.tsx\");\n/* harmony import */ var _lib_queryClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/queryClient */ \"(ssr)/./src/lib/queryClient.ts\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\nfunction Providers({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ErrorBoundary__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n            client: _lib_queryClient__WEBPACK_IMPORTED_MODULE_2__.queryClient,\n            children: [\n                children,\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                    initialIsOpen: false\n                }, void 0, false, {\n                    fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\components\\\\providers.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTJEO0FBQ1E7QUFDYjtBQUNQO0FBRXhDLFNBQVNJLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxxQkFDRSw4REFBQ0gsaUVBQWFBO2tCQUNaLDRFQUFDRixzRUFBbUJBO1lBQUNNLFFBQVFILHlEQUFXQTs7Z0JBQ3JDRTtnQkFYVCxLQVlrQyxrQkFDeEIsOERBQUNKLDhFQUFrQkE7b0JBQUNNLGVBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSzdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGVnYWwtZG9jdW1lbnQtYWktZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMudHN4P2JlODciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFF1ZXJ5Q2xpZW50UHJvdmlkZXIgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnknXG5pbXBvcnQgeyBSZWFjdFF1ZXJ5RGV2dG9vbHMgfSBmcm9tICdAdGFuc3RhY2svcmVhY3QtcXVlcnktZGV2dG9vbHMnXG5pbXBvcnQgRXJyb3JCb3VuZGFyeSBmcm9tICdAL2NvbXBvbmVudHMvRXJyb3JCb3VuZGFyeSdcbmltcG9ydCB7IHF1ZXJ5Q2xpZW50IH0gZnJvbSAnQC9saWIvcXVlcnlDbGllbnQnXG5cbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICByZXR1cm4gKFxuICAgIDxFcnJvckJvdW5kYXJ5PlxuICAgICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAge3Byb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnICYmIChcbiAgICAgICAgICA8UmVhY3RRdWVyeURldnRvb2xzIGluaXRpYWxJc09wZW49e2ZhbHNlfSAvPlxuICAgICAgICApfVxuICAgICAgPC9RdWVyeUNsaWVudFByb3ZpZGVyPlxuICAgIDwvRXJyb3JCb3VuZGFyeT5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJSZWFjdFF1ZXJ5RGV2dG9vbHMiLCJFcnJvckJvdW5kYXJ5IiwicXVlcnlDbGllbnQiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsImNsaWVudCIsImluaXRpYWxJc09wZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/queryClient.ts":
/*!********************************!*\
  !*** ./src/lib/queryClient.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryClient: () => (/* binding */ queryClient),\n/* harmony export */   queryKeys: () => (/* binding */ queryKeys),\n/* harmony export */   queryOptions: () => (/* binding */ queryOptions)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/lib/queryClient.mjs\");\n\nconst queryConfig = {\n    queries: {\n        // 数据保持新鲜的时间（5分钟）\n        staleTime: 5 * 60 * 1000,\n        // 缓存时间（10分钟）\n        gcTime: 10 * 60 * 1000,\n        // 重试配置\n        retry: (failureCount, error)=>{\n            // 对于4xx错误不重试\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            // 最多重试3次\n            return failureCount < 3;\n        },\n        // 重试延迟\n        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n        // 窗口重新获得焦点时重新获取数据\n        refetchOnWindowFocus: false,\n        // 网络重连时重新获取数据\n        refetchOnReconnect: true\n    },\n    mutations: {\n        // 变更重试配置\n        retry: (failureCount, error)=>{\n            if (error?.status >= 400 && error?.status < 500) {\n                return false;\n            }\n            return failureCount < 2;\n        }\n    }\n};\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.QueryClient({\n    defaultOptions: queryConfig\n});\n// 查询键工厂\nconst queryKeys = {\n    // 聊天相关\n    chat: {\n        all: [\n            \"chat\"\n        ],\n        sessions: ()=>[\n                ...queryKeys.chat.all,\n                \"sessions\"\n            ],\n        session: (id)=>[\n                ...queryKeys.chat.sessions(),\n                id\n            ],\n        history: (sessionId, page)=>[\n                ...queryKeys.chat.session(sessionId),\n                \"history\",\n                page\n            ],\n        context: (sessionId)=>[\n                ...queryKeys.chat.session(sessionId),\n                \"context\"\n            ]\n    },\n    // 推荐相关\n    recommendations: {\n        all: [\n            \"recommendations\"\n        ],\n        session: (sessionId)=>[\n                ...queryKeys.recommendations.all,\n                \"session\",\n                sessionId\n            ],\n        popular: (category)=>[\n                ...queryKeys.recommendations.all,\n                \"popular\",\n                category\n            ]\n    },\n    // 模板相关\n    templates: {\n        all: [\n            \"templates\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.templates.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.templates.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.templates.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.templates.all,\n                \"search\",\n                query,\n                filters\n            ]\n    },\n    // 案例相关\n    cases: {\n        all: [\n            \"cases\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.cases.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.cases.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.cases.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.cases.all,\n                \"search\",\n                query,\n                filters\n            ],\n        similar: (id)=>[\n                ...queryKeys.cases.all,\n                \"similar\",\n                id\n            ]\n    },\n    // 知识库相关\n    knowledge: {\n        all: [\n            \"knowledge\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.knowledge.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.knowledge.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.knowledge.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.knowledge.all,\n                \"search\",\n                query,\n                filters\n            ],\n        categories: ()=>[\n                ...queryKeys.knowledge.all,\n                \"categories\"\n            ]\n    },\n    // 指南相关\n    guides: {\n        all: [\n            \"guides\"\n        ],\n        list: (filters)=>[\n                ...queryKeys.guides.all,\n                \"list\",\n                filters\n            ],\n        detail: (id)=>[\n                ...queryKeys.guides.all,\n                \"detail\",\n                id\n            ],\n        popular: (category)=>[\n                ...queryKeys.guides.all,\n                \"popular\",\n                category\n            ],\n        search: (query, filters)=>[\n                ...queryKeys.guides.all,\n                \"search\",\n                query,\n                filters\n            ]\n    },\n    // 用户相关\n    user: {\n        all: [\n            \"user\"\n        ],\n        profile: ()=>[\n                ...queryKeys.user.all,\n                \"profile\"\n            ],\n        preferences: ()=>[\n                ...queryKeys.user.all,\n                \"preferences\"\n            ],\n        history: ()=>[\n                ...queryKeys.user.all,\n                \"history\"\n            ]\n    },\n    // 系统相关\n    system: {\n        all: [\n            \"system\"\n        ],\n        health: ()=>[\n                ...queryKeys.system.all,\n                \"health\"\n            ],\n        stats: ()=>[\n                ...queryKeys.system.all,\n                \"stats\"\n            ],\n        websocket: ()=>[\n                ...queryKeys.system.all,\n                \"websocket\"\n            ]\n    }\n};\n// 查询选项预设\nconst queryOptions = {\n    // 实时数据（短缓存）\n    realtime: {\n        staleTime: 0,\n        gcTime: 1 * 60 * 1000,\n        refetchInterval: 30 * 1000\n    },\n    // 静态数据（长缓存）\n    static: {\n        staleTime: 60 * 60 * 1000,\n        gcTime: 24 * 60 * 60 * 1000,\n        refetchOnWindowFocus: false,\n        refetchOnReconnect: false\n    },\n    // 用户数据（中等缓存）\n    user: {\n        staleTime: 10 * 60 * 1000,\n        gcTime: 30 * 60 * 1000\n    },\n    // 搜索结果（短缓存）\n    search: {\n        staleTime: 2 * 60 * 1000,\n        gcTime: 5 * 60 * 1000\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/queryClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dca4c53cce0c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbGVnYWwtZG9jdW1lbnQtYWktZnJvbnRlbmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzA3OGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkY2E0YzUzY2NlMGNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers */ \"(rsc)/./src/components/providers.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\nconst metadata = {\n    title: \"智能法律文书生成系统\",\n    description: \"基于AI大模型的智能法律文书生成应用，支持移动端优先的响应式设计\",\n    keywords: [\n        \"法律文书\",\n        \"AI\",\n        \"智能生成\",\n        \"起诉状\",\n        \"答辞状\"\n    ],\n    authors: [\n        {\n            name: \"智能法律文书生成系统\"\n        }\n    ],\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1,\n        maximumScale: 1\n    },\n    themeColor: \"#3b82f6\",\n    manifest: \"/manifest.json\",\n    icons: {\n        icon: \"/favicon.ico\",\n        apple: \"/apple-touch-icon.png\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} h-full bg-gray-50 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-full\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-center\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#10b981\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\Intelligent_legal_document_generation\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`H:\Intelligent_legal_document_generation\frontend\src\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/providers.tsx":
/*!**************************************!*\
  !*** ./src/components/providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`H:\Intelligent_legal_document_generation\frontend\src\components\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@tanstack","vendor-chunks/superjson","vendor-chunks/react-hot-toast","vendor-chunks/lucide-react","vendor-chunks/use-sync-external-store","vendor-chunks/is-what","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/copy-anything"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CIntelligent_legal_document_generation%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
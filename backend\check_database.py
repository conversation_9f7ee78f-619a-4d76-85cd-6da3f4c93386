#!/usr/bin/env python3
"""
检查数据库连接状态和配置
"""
import psycopg2
import sys
from urllib.parse import urlparse

def check_database_connection():
    """检查数据库连接"""
    print("🔍 检查数据库连接...")
    
    # 数据库连接字符串
    db_url = "postgresql://legal_user:LegalDoc2024%21%40%23@***************:9856/legal_documents"
    
    # 解析连接字符串
    parsed = urlparse(db_url)
    print(f"数据库主机: {parsed.hostname}")
    print(f"数据库端口: {parsed.port}")
    print(f"数据库名称: {parsed.path[1:]}")  # 去掉开头的 /
    print(f"用户名: {parsed.username}")
    
    try:
        print("\n尝试连接数据库...")
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ 数据库连接成功!")
        print(f"PostgreSQL版本: {version[0]}")
        
        # 检查用户表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'users'
            );
        """)
        users_table_exists = cursor.fetchone()[0]
        print(f"用户表存在: {'✅ 是' if users_table_exists else '❌ 否'}")
        
        if users_table_exists:
            # 检查用户数量
            cursor.execute("SELECT COUNT(*) FROM users;")
            user_count = cursor.fetchone()[0]
            print(f"用户数量: {user_count}")
            
            if user_count > 0:
                # 显示用户信息
                cursor.execute("SELECT username, email, is_admin, is_active FROM users LIMIT 5;")
                users = cursor.fetchall()
                print("\n用户列表:")
                for user in users:
                    username, email, is_admin, is_active = user
                    admin_flag = "👑" if is_admin else "👤"
                    status_flag = "✅" if is_active else "❌"
                    print(f"  {admin_flag} {username} ({email}) - {status_flag}")
        
        cursor.close()
        conn.close()
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n可能的原因:")
        print("1. PostgreSQL服务未启动")
        print("2. 网络连接问题")
        print("3. 防火墙阻止连接")
        print("4. 数据库凭据错误")
        print("5. 数据库不存在")
        return False
        
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def check_redis_connection():
    """检查Redis连接"""
    print("\n🔍 检查Redis连接...")
    
    try:
        import redis
        r = redis.Redis(host='***************', port=9857, decode_responses=True)
        r.ping()
        print("✅ Redis连接成功!")
        
        # 获取Redis信息
        info = r.info()
        print(f"Redis版本: {info.get('redis_version', 'Unknown')}")
        print(f"已用内存: {info.get('used_memory_human', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "="*60)
    print("🛠️ 数据库连接问题解决方案")
    print("="*60)
    
    print("\n1. 检查Synology NAS服务状态:")
    print("   • 登录Synology DSM管理界面")
    print("   • 检查PostgreSQL套件是否正在运行")
    print("   • 检查端口9856是否开放")
    
    print("\n2. 检查网络连接:")
    print("   • ping ***************")
    print("   • telnet 192.168.************")
    
    print("\n3. 检查防火墙设置:")
    print("   • Synology防火墙规则")
    print("   • Windows防火墙设置")
    print("   • 路由器端口转发")
    
    print("\n4. 检查数据库配置:")
    print("   • PostgreSQL配置文件")
    print("   • 用户权限设置")
    print("   • 数据库是否存在")
    
    print("\n5. 临时解决方案:")
    print("   • 使用本地PostgreSQL")
    print("   • 启用内存认证回退（仅开发环境）")
    print("   • 修改数据库连接字符串")

def main():
    """主函数"""
    print("="*60)
    print("数据库连接检查工具")
    print("="*60)
    
    # 检查数据库连接
    db_ok = check_database_connection()
    
    # 检查Redis连接
    redis_ok = check_redis_connection()
    
    print("\n" + "="*60)
    print("检查结果总结")
    print("="*60)
    print(f"PostgreSQL: {'✅ 正常' if db_ok else '❌ 异常'}")
    print(f"Redis: {'✅ 正常' if redis_ok else '❌ 异常'}")
    
    if not db_ok:
        provide_solutions()
        print("\n⚠️ 建议: 在数据库问题解决前，可以使用内存认证进行开发")
        print("设置环境变量: ALLOW_MEMORY_AUTH_FALLBACK=true")
    else:
        print("\n🎉 所有服务正常！建议禁用内存认证回退以提高安全性")
        print("设置环境变量: ALLOW_MEMORY_AUTH_FALLBACK=false")

if __name__ == "__main__":
    main()
